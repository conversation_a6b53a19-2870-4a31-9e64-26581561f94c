# JavaScript加密还原工具

基于AI分析报告和调试日志实现的Node.js加密还原工具，专门用于还原CryptoJS加密的数据。

## 📋 功能特性

- 🔓 **多种解密方法**：支持静态密钥、KDF派生密钥、PasswordBasedCipher等
- 🤖 **智能解密**：自动尝试多种解密方法和加密模式
- 🔍 **暴力破解**：支持密码字典暴力破解
- 📊 **多种加密模式**：CFB、CTR、OFB、CBC、ECB
- 🧂 **Salt支持**：支持带Salt的PBKDF2密钥派生
- 📝 **详细日志**：提供详细的解密过程日志

## 🚀 快速开始

### 安装依赖

```bash
npm install crypto-js
```

### 基本使用

```javascript
const CryptoReverse = require('./crypto_reverse');
const crypto = new CryptoReverse();

// 智能解密 - 推荐方法
const result = crypto.smartDecrypt(encryptedData, possiblePassword);

if (result.success) {
    console.log('解密成功:', result.result);
    console.log('使用方法:', result.method);
} else {
    console.log('解密失败:', result.error);
}
```

## 📖 详细使用方法

### 1. 智能解密（推荐）

自动尝试多种解密方法：

```javascript
const crypto = new CryptoReverse();

// 基本智能解密
const result = crypto.smartDecrypt(encryptedData, password);

// 带自定义密钥的智能解密
const result = crypto.smartDecrypt(encryptedData, password, customKey);
```

### 2. 静态密钥解密

如果知道使用了固定密钥：

```javascript
// 使用默认密钥 'mysecretkey12345'
const result = crypto.staticKeyDecrypt(encryptedData);

// 使用自定义密钥
const result = crypto.staticKeyDecrypt(encryptedData, 'your_custom_key');
```

### 3. KDF派生密钥解密

如果使用了PBKDF2密钥派生：

```javascript
// 自动提取Salt
const result = crypto.kdfDecrypt(encryptedData, password);

// 指定Salt（十六进制）
const result = crypto.kdfDecrypt(encryptedData, password, 'a1b2c3d4e5f6g7h8');
```

### 4. PasswordBasedCipher解密

CryptoJS的PasswordBasedCipher模式：

```javascript
const result = crypto.passwordBasedDecrypt(encryptedData, password);
```

### 5. Base64解码

简单的Base64编码：

```javascript
const result = crypto.base64Decode(encodedData);
```

### 6. 暴力破解

使用密码字典：

```javascript
// 使用默认密码字典
const result = crypto.bruteForce(encryptedData);

// 使用自定义密码字典
const passwords = ['password1', 'password2', 'secret123'];
const result = crypto.bruteForce(encryptedData, passwords);
```

## 🔧 运行测试

```bash
# 运行完整测试套件
npm test

# 或直接运行
node test_example.js
```

## 📊 支持的加密参数

### 加密算法
- AES (Advanced Encryption Standard)

### 加密模式
- CFB (Cipher Feedback)
- CTR (Counter)
- OFB (Output Feedback) 
- CBC (Cipher Block Chaining)
- ECB (Electronic Codebook)

### 密钥派生
- PBKDF2 (Password-Based Key Derivation Function 2)
- 静态密钥
- 动态Salt

### 默认参数
- 默认密钥：`mysecretkey12345`
- IV长度：16字节
- Salt长度：8字节
- PBKDF2迭代次数：1000

## 💡 使用技巧

### 1. 数据格式
- 加密数据通常是Base64格式
- 确保数据完整，没有换行符或空格

### 2. 密码猜测
常见的密码模式：
- 网站域名
- 'password', '123456', 'admin'
- 开发者姓名或项目名
- 从JavaScript代码中提取的字符串

### 3. 调试建议
- 查看浏览器开发者工具的Network标签
- 搜索JavaScript代码中的加密相关字符串
- 查找`CryptoJS`、`AES`、`encrypt`等关键词

## 🔍 基于AI分析的特性

本工具基于以下AI分析结果设计：

1. **CryptoJS库识别**：自动识别CryptoJS库的使用模式
2. **加密模式检测**：支持CFB、CTR、OFB、CTRGladman等模式
3. **密钥提取**：从调试日志中提取静态密钥`mysecretkey12345`
4. **KDF模式支持**：模拟`kdf.execute()`的密钥派生过程
5. **PasswordBasedCipher**：还原CryptoJS的PasswordBasedCipher实现

## 📝 返回值格式

成功时：
```javascript
{
    success: true,
    method: 'StaticKey-CFB',  // 使用的解密方法
    result: 'decrypted text', // 解密结果
    key: 'used_key',         // 使用的密钥（可选）
    salt: 'salt_hex'         // 使用的Salt（可选）
}
```

失败时：
```javascript
{
    success: false,
    method: 'attempted_method',
    error: 'error description'
}
```

## ⚠️ 注意事项

1. **合法使用**：仅用于合法的安全测试和学习目的
2. **数据安全**：不要在生产环境中使用测试密钥
3. **性能考虑**：暴力破解可能耗时较长
4. **兼容性**：需要Node.js环境和crypto-js库

## 🤝 贡献

欢迎提出Issue和Pull Request来改进这个工具。

## 📄 许可证

MIT License

## 🔗 相关链接

- [CryptoJS官方文档](https://cryptojs.gitbook.io/docs/)
- [AES加密标准](https://en.wikipedia.org/wiki/Advanced_Encryption_Standard)
- [PBKDF2规范](https://tools.ietf.org/html/rfc2898)

---

⭐ 如果这个工具对你有帮助，请给项目点个星！
