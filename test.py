import sys
import json
import os
import asyncio
import aiohttp
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QLineEdit, QPushButton, QTextEdit, 
                           QLabel, QProgressBar, QMessageBox, QComboBox,
                           QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView,
                           QSplitter, QFrame, QDialog, QFormLayout, QGraphicsView,
                           QGraphicsScene, QStackedWidget, QScrollArea)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings, QTimer, QRectF, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QPainter, QColor, QLinearGradient, QPen, QBrush, QPainterPath, QFont, QPalette
import requests
import time
from typing import List, Dict, Optional
from datetime import datetime
import random
import multiprocessing

class LightPalette(QPalette):
    def __init__(self):
        super().__init__()
        # 设置亮色主题
        self.setColor(QPalette.ColorRole.Window, QColor(245, 245, 245))
        self.setColor(QPalette.ColorRole.WindowText, QColor(50, 50, 50))
        self.setColor(QPalette.ColorRole.Base, QColor(255, 255, 255))
        self.setColor(QPalette.ColorRole.AlternateBase, QColor(240, 240, 240))
        self.setColor(QPalette.ColorRole.ToolTipBase, QColor(255, 255, 255))
        self.setColor(QPalette.ColorRole.ToolTipText, QColor(50, 50, 50))
        self.setColor(QPalette.ColorRole.Text, QColor(50, 50, 50))
        self.setColor(QPalette.ColorRole.Button, QColor(240, 240, 240))
        self.setColor(QPalette.ColorRole.ButtonText, QColor(50, 50, 50))
        self.setColor(QPalette.ColorRole.BrightText, QColor(0, 0, 0))
        self.setColor(QPalette.ColorRole.Link, QColor(0, 120, 215))
        self.setColor(QPalette.ColorRole.Highlight, QColor(0, 120, 215))
        self.setColor(QPalette.ColorRole.HighlightedText, QColor(255, 255, 255))

class ModernButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(40)
        self.setStyleSheet("""
            QPushButton {
                background-color: #0078D7;
                border: none;
                border-radius: 5px;
                color: white;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1988E0;
            }
            QPushButton:pressed {
                background-color: #006CC1;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
            }
        """)

class ModernComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(40)
        self.setStyleSheet("""
            QComboBox {
                background-color: #FFFFFF;
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                color: #333333;
                padding: 8px 16px;
                font-size: 14px;
            }
            QComboBox:hover {
                border: 1px solid #999999;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)

class DataVisualizationWidget(QGraphicsView):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.FullViewportUpdate)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 设置背景
        self.setStyleSheet("background: #FFFFFF;")
        
        # 初始化数据点
        self.data_points = []
        self.animations = []
        
        # 设置定时器用于动画更新
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_visualization)
        self.update_timer.start(50)  # 20fps

    def add_data_point(self, success: bool, latency: float):
        """添加新的数据点"""
        color = QColor(46, 204, 113) if success else QColor(231, 76, 60)
        point = {
            'x': len(self.data_points) * 30,
            'y': latency * 20,  # 缩放延迟值以适应显示
            'color': color,
            'size': 10,
            'alpha': 255
        }
        self.data_points.append(point)
        
        # 创建动画效果
        self.create_point_animation(point)
        
        # 保持最新的100个点
        if len(self.data_points) > 100:
            self.data_points.pop(0)
            
        self.scene.update()

    def create_point_animation(self, point):
        """为数据点创建动画效果"""
        # 这里可以添加更多动画效果
        point['size'] = 20  # 开始时较大
        point['alpha'] = 0  # 开始时透明
        
        def update_animation():
            if point['size'] > 10:
                point['size'] -= 0.5
            if point['alpha'] < 255:
                point['alpha'] += 15
            self.scene.update()
            
        timer = QTimer(self)
        timer.timeout.connect(update_animation)
        timer.start(50)
        self.animations.append(timer)

    def update_visualization(self):
        """更新可视化效果"""
        self.scene.clear()
        
        # 绘制网格
        pen = QPen(QColor(230, 230, 230))
        for i in range(0, self.width(), 50):
            self.scene.addLine(i, 0, i, self.height(), pen)
        for i in range(0, self.height(), 50):
            self.scene.addLine(0, i, self.width(), i, pen)
        
        # 绘制数据点和连线
        if self.data_points:
            path = QPainterPath()
            path.moveTo(self.data_points[0]['x'], self.data_points[0]['y'])
            
            for i in range(len(self.data_points)):
                point = self.data_points[i]
                
                # 绘制点
                brush = QBrush(QColor(point['color'].red(), 
                                    point['color'].green(),
                                    point['color'].blue(),
                                    point['alpha']))
                self.scene.addEllipse(point['x'] - point['size']/2,
                                    point['y'] - point['size']/2,
                                    point['size'], point['size'],
                                    QPen(Qt.PenStyle.NoPen), brush)
                
                # 添加到路径
                if i > 0:
                    path.lineTo(point['x'], point['y'])
            
            # 绘制连线
            pen = QPen(QColor(200, 200, 200, 100))
            pen.setWidth(2)
            self.scene.addPath(path, pen)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.scene.setSceneRect(0, 0, self.width(), self.height())

class EditConfigDialog(QDialog):
    """编辑配置对话框"""
    def __init__(self, config_name="", api_key="", base_url="", parent=None):
        super().__init__(parent)
        self.setWindowTitle("编辑配置")
        self.setMinimumWidth(400)
        
        layout = QFormLayout()
        self.setLayout(layout)
        
        # 如果是编辑模式，配置名称不可修改
        self.name_input = QLineEdit(config_name)
        if config_name:
            self.name_input.setEnabled(False)
        
        self.key_input = QLineEdit(api_key)
        self.url_input = QLineEdit(base_url)
        if not base_url:
            self.url_input.setText("https://api.example.com/v1")
        
        layout.addRow("配置名称:", self.name_input)
        layout.addRow("API Key:", self.key_input)
        layout.addRow("Base URL:", self.url_input)
        
        # 添加测试连接按钮
        test_btn = QPushButton("测试连接")
        test_btn.clicked.connect(self.test_connection)
        layout.addRow("", test_btn)
        
        buttons = QHBoxLayout()
        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.accept)
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        buttons.addWidget(save_btn)
        buttons.addWidget(cancel_btn)
        layout.addRow("", buttons)
        
    def test_connection(self):
        """测试API连接"""
        api_key = self.key_input.text()
        base_url = self.url_input.text()
        
        try:
            response = requests.get(
                f"{base_url}/models",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                timeout=10
            )
            
            if response.status_code == 200:
                QMessageBox.information(self, "连接测试", "连接成功！API正常工作。")
            else:
                QMessageBox.warning(self, "连接测试", f"连接失败: HTTP {response.status_code}\n{response.text}")
        except Exception as e:
            QMessageBox.warning(self, "连接测试", f"连接失败: {str(e)}")

class ConfigDialog(QDialog):
    """配置管理对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("配置管理")
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 配置表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["名称", "API Key", "Base URL", "操作"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        layout.addWidget(self.table)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加新配置")
        self.add_btn.clicked.connect(self.add_config)
        btn_layout.addWidget(self.add_btn)
        layout.addLayout(btn_layout)
        
        self.load_configs()
        
    def load_configs(self):
        """加载所有保存的配置"""
        settings = QSettings('AIModelTester', 'Configurations')
        configs = settings.value('configs', {})
        
        self.table.setRowCount(len(configs))
        for i, (name, config) in enumerate(configs.items()):
            self.table.setItem(i, 0, QTableWidgetItem(name))
            # 对API Key进行掩码处理
            api_key = config.get('api_key', '')
            masked_key = self._mask_api_key(api_key)
            self.table.setItem(i, 1, QTableWidgetItem(masked_key))
            self.table.setItem(i, 2, QTableWidgetItem(config.get('base_url', '')))
            
            # 创建操作按钮容器
            btn_widget = QWidget()
            btn_layout = QHBoxLayout(btn_widget)
            btn_layout.setContentsMargins(0, 0, 0, 0)
            
            # 编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.clicked.connect(lambda checked, row=i: self.edit_config(row))
            btn_layout.addWidget(edit_btn)
            
            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, row=i: self.delete_config(row))
            btn_layout.addWidget(delete_btn)
            
            self.table.setCellWidget(i, 3, btn_widget)
    
    def _mask_api_key(self, api_key):
        """对API Key进行掩码处理"""
        if not api_key:
            return ""
        if len(api_key) <= 8:
            return "*" * len(api_key)
        return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]
    
    def add_config(self):
        """添加新配置"""
        dialog = EditConfigDialog(parent=self)
        if dialog.exec():
            name = dialog.name_input.text()
            api_key = dialog.key_input.text()
            base_url = dialog.url_input.text()
            
            if not name or not api_key or not base_url:
                QMessageBox.warning(self, "错误", "所有字段都必须填写")
                return
            
            settings = QSettings('AIModelTester', 'Configurations')
            configs = settings.value('configs', {})
            
            if name in configs:
                QMessageBox.warning(self, "错误", "配置名称已存在")
                return
            
            configs[name] = {
                'api_key': api_key,
                'base_url': base_url
            }
            settings.setValue('configs', configs)
            self.load_configs()
    
    def edit_config(self, row):
        """编辑配置"""
        name = self.table.item(row, 0).text()
        settings = QSettings('AIModelTester', 'Configurations')
        configs = settings.value('configs', {})
        
        if name in configs:
            config = configs[name]
            dialog = EditConfigDialog(
                config_name=name,
                api_key=config['api_key'],
                base_url=config['base_url'],
                parent=self
            )
            
            if dialog.exec():
                api_key = dialog.key_input.text()
                base_url = dialog.url_input.text()
                
                if not api_key or not base_url:
                    QMessageBox.warning(self, "错误", "所有字段都必须填写")
                    return
                
                configs[name] = {
                    'api_key': api_key,
                    'base_url': base_url
                }
                settings.setValue('configs', configs)
                self.load_configs()
    
    def delete_config(self, row):
        """删除配置"""
        name = self.table.item(row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除配置 '{name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            settings = QSettings('AIModelTester', 'Configurations')
            configs = settings.value('configs', {})
            if name in configs:
                del configs[name]
                settings.setValue('configs', configs)
                self.load_configs()

class StreamResponseHandler:
    def __init__(self):
        self.buffer = ""
        self.complete_response = ""
        self.last_chunk = ""
        self.accumulated_chunk = ""
        
    def process_chunk(self, chunk):
        try:
            # 处理SSE格式的数据
            if chunk.startswith('data: '):
                data = chunk[6:]  # 移除 'data: ' 前缀
                if data.strip() == '[DONE]':
                    return None
                
                try:
                    json_data = json.loads(data)
                    if 'choices' in json_data and len(json_data['choices']) > 0:
                        delta = json_data['choices'][0].get('delta', {})
                        if 'content' in delta:
                            content = delta['content']
                            self.complete_response += content
                            # 累积内容直到形成完整的词或句子
                            self.accumulated_chunk += content
                            if self._is_complete_segment(self.accumulated_chunk):
                                chunk_to_return = self.accumulated_chunk
                                self.accumulated_chunk = ""
                                return chunk_to_return
                            return None
                except json.JSONDecodeError:
                    pass
            return None
        except Exception as e:
            print(f"Error processing chunk: {str(e)}")
            return None
            
    def _is_complete_segment(self, text):
        """判断文本是否构成一个完整的语义片段"""
        # 如果包含标点符号或空格，认为是一个完整片段
        if any(char in text for char in '。，！？.;!?, '):
            return True
        # 如果累积了足够多的字符（例如5个字符），也返回
        if len(text) >= 5:
            return True
        return False

class AsyncModelTestWorker(QThread):
    """异步模型测试工作线程"""
    progress_updated = pyqtSignal(str)
    test_completed = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    result_ready = pyqtSignal(dict)
    model_count_updated = pyqtSignal(int, int)  # 新增：发送总数和当前进度
    
    def __init__(self, api_key: str, base_url: str, max_workers: Optional[int] = None):
        super().__init__()
        self.api_key = api_key
        self.base_url = base_url
        self.models = []
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        self.completed_count = 0
        
    async def test_single_model(self, model_id: str, session: aiohttp.ClientSession) -> Dict:
        """异步测试单个模型"""
        self.progress_updated.emit(f"开始测试模型: {model_id}")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": model_id,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个AI助手。请用中文回复。"
                },
                {
                    "role": "user",
                    "content": "你好，请简单介绍一下你自己。"
                }
            ],
            "stream": True
        }
        
        result = {
            'model_id': model_id,
            'success': False,
            'latency': 0,
            'response': '',
            'error': ''
        }
        
        try:
            start_time = time.time()
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            ) as response:
                if response.status == 200:
                    stream_handler = StreamResponseHandler()
                    async for line in response.content:
                        if line:
                            decoded_line = line.decode('utf-8')
                            content = stream_handler.process_chunk(decoded_line)
                            if content:
                                self.progress_updated.emit(f"  实时响应 ({model_id}): {content}")
                    
                    end_time = time.time()
                    result['latency'] = end_time - start_time
                    result['response'] = stream_handler.complete_response
                    result['success'] = True
                    
                    response_preview = stream_handler.complete_response[:100]
                    if len(stream_handler.complete_response) > 100:
                        response_preview += "..."
                        
                    self.progress_updated.emit(
                        f"✓ 模型 {model_id} 可用\n"
                        f"  响应时间: {result['latency']:.2f}秒\n"
                        f"  回复示例: {response_preview}\n"
                    )
                else:
                    error_message = await response.text()
                    try:
                        error_json = await response.json()
                        if isinstance(error_json, dict) and 'error' in error_json:
                            error_message = json.dumps(error_json['error'], ensure_ascii=False)
                    except:
                        pass
                        
                    result['error'] = f"HTTP {response.status}: {error_message}"
                    self.progress_updated.emit(
                        f"✗ 模型 {model_id} 不可用\n"
                        f"  错误代码: HTTP {response.status}\n"
                        f"  错误信息: {error_message}\n"
                    )
        except asyncio.TimeoutError:
            result['error'] = "请求超时"
            self.progress_updated.emit(f"✗ 模型 {model_id} 请求超时\n")
        except Exception as e:
            result['error'] = str(e)
            self.progress_updated.emit(f"✗ 模型 {model_id} 测试出错: {str(e)}\n")
        finally:
            # 更新进度
            self.completed_count += 1
            self.model_count_updated.emit(len(self.models), self.completed_count)
            # 立即发送结果
            self.result_ready.emit(result)
            
        return result

    async def get_models(self, session: aiohttp.ClientSession) -> List[Dict]:
        """异步获取可用模型列表"""
        try:
            async with session.get(
                f"{self.base_url}/models",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.models = data.get('data', [])
                    return self.models
                else:
                    self.progress_updated.emit(f"获取模型列表失败: HTTP {response.status}")
                    return []
        except Exception as e:
            self.progress_updated.emit(f"获取模型列表时出错: {str(e)}")
            return []

    async def run_async(self):
        """异步运行测试"""
        try:
            async with aiohttp.ClientSession() as session:
                self.progress_updated.emit("正在获取可用模型列表...")
                models = await self.get_models(session)
                
                if not models:
                    self.error_occurred.emit("无法获取模型列表")
                    return

                self.progress_updated.emit(f"\n找到 {len(models)} 个模型，开始并发测试...\n")
                self.model_count_updated.emit(len(models), 0)  # 初始化进度
                
                # 创建测试任务
                tasks = []
                for model in models:
                    model_id = model['id']
                    tasks.append(self.test_single_model(model_id, session))
                
                # 并发执行所有测试任务
                results = await asyncio.gather(*tasks)
                
                # 处理结果
                working_models = [result['model_id'] for result in results if result['success']]
                self.test_completed.emit(working_models)

        except Exception as e:
            self.error_occurred.emit(f"发生错误: {str(e)}")

    def run(self):
        """QThread的运行方法"""
        try:
            # 在Windows上需要使用这种方式运行事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.run_async())
        except Exception as e:
            self.error_occurred.emit(f"运行错误: {str(e)}")
        finally:
            loop.close()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI模型测试工具")
        # 设置更合理的默认尺寸
        self.setMinimumSize(960, 640)
        self.resize(1024, 768)
        
        # 设置亮色主题
        self.setPalette(LightPalette())
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建主布局
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        main_widget.setLayout(layout)
        
        # 顶部工具栏（更紧凑的设计）
        toolbar = QHBoxLayout()
        toolbar.setSpacing(10)
        toolbar.setContentsMargins(0, 0, 0, 10)
        
        # 配置选择下拉框（减小宽度）
        config_layout = QHBoxLayout()
        config_layout.setSpacing(5)
        self.config_combo = ModernComboBox()
        self.config_combo.setMaximumWidth(200)
        self.config_combo.currentIndexChanged.connect(self.load_selected_config)
        config_label = QLabel("配置:")
        config_label.setStyleSheet("color: #cccccc;")
        config_layout.addWidget(config_label)
        config_layout.addWidget(self.config_combo)
        toolbar.addLayout(config_layout)
        
        # 配置管理和测试按钮（使用图标按钮节省空间）
        config_btn = ModernButton("配置")
        config_btn.setMaximumWidth(80)
        config_btn.clicked.connect(self.show_config_dialog)
        toolbar.addWidget(config_btn)
        
        self.test_button = ModernButton("开始测试")
        self.test_button.setMaximumWidth(100)
        self.test_button.clicked.connect(self.start_test)
        toolbar.addWidget(self.test_button)
        
        # 添加弹性空间
        toolbar.addStretch()
        
        layout.addLayout(toolbar)
        
        # 创建分割器用于调整面板大小
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background: #E0E0E0;
                width: 2px;
            }
        """)
        
        # 左侧面板（结果和可视化）
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(10)
        left_layout.setContentsMargins(0, 0, 10, 0)
        
        # 数据可视化区域（调整高度比例）
        self.visualization = DataVisualizationWidget()
        self.visualization.setMinimumHeight(200)
        left_layout.addWidget(self.visualization, stretch=1)
        
        # 测试结果表格（更紧凑的设计）
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["模型", "状态", "延迟(秒)", "错误信息"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        self.results_table.verticalHeader().setDefaultSectionSize(30)  # 减小行高
        self.results_table.setStyleSheet("""
            QTableWidget {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                gridline-color: #E0E0E0;
                color: #000000;  /* 设置默认文本颜色为黑色 */
            }
            QTableWidget::item {
                padding: 4px;
                border: none;
                color: #000000;  /* 设置单元格文本颜色为黑色 */
            }
            QHeaderView::section {
                background-color: #F5F5F5;
                padding: 4px;
                border: none;
                border-bottom: 1px solid #E0E0E0;
                color: #000000;  /* 设置表头文本颜色为黑色 */
                font-weight: bold;
            }
        """)
        left_layout.addWidget(self.results_table, stretch=1)
        
        splitter.addWidget(left_panel)
        
        # 右侧面板（状态和日志）
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(10, 0, 0, 0)
        
        # 状态面板（更紧凑的设计）
        status_panel = QFrame()
        status_panel.setMaximumHeight(100)
        status_panel.setStyleSheet("""
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        status_layout = QVBoxLayout(status_panel)
        status_layout.setSpacing(5)
        
        self.status_label = QLabel("等待测试...")
        self.status_label.setStyleSheet("font-size: 14px; color: #333333;")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 3px;
                background-color: #F0F0F0;
                height: 6px;
            }
            QProgressBar::chunk {
                background-color: #0078D7;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.progress_bar)
        
        right_layout.addWidget(status_panel)
        
        # 日志面板
        log_panel = QFrame()
        log_panel.setStyleSheet("""
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        log_layout = QVBoxLayout(log_panel)
        log_layout.setSpacing(5)
        
        # 日志标题
        log_header = QHBoxLayout()
        log_label = QLabel("测试日志")
        log_label.setStyleSheet("color: #333333; font-weight: bold;")
        log_header.addWidget(log_label)
        log_header.addStretch()
        log_layout.addLayout(log_header)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                color: #333333;
                padding: 5px;
                font-family: "Consolas", monospace;
                font-size: 12px;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        right_layout.addWidget(log_panel)
        
        splitter.addWidget(right_panel)
        
        # 设置分割器的初始大小比例
        splitter.setStretchFactor(0, 2)  # 左侧面板占2
        splitter.setStretchFactor(1, 1)  # 右侧面板占1
        
        layout.addWidget(splitter)
        
        self.worker = None
        self.load_configs()
        
        # 创建历史记录目录
        self.history_dir = Path.home() / '.ai_model_tester' / 'history'
        self.history_dir.mkdir(parents=True, exist_ok=True)
        
        # 在__init__方法中添加进度显示
        self.total_models = 0
        self.completed_models = 0

    def show_config_dialog(self):
        """显示配置管理对话框"""
        dialog = ConfigDialog(self)
        dialog.exec()
        self.load_configs()

    def load_configs(self):
        """加载所有配置到下拉框"""
        self.config_combo.clear()
        settings = QSettings('AIModelTester', 'Configurations')
        configs = settings.value('configs', {})
        for name in configs.keys():
            self.config_combo.addItem(name)

    def load_selected_config(self):
        """加载选中的配置"""
        current_config = self.config_combo.currentText()
        if current_config:
            settings = QSettings('AIModelTester', 'Configurations')
            configs = settings.value('configs', {})
            if current_config in configs:
                config = configs[current_config]

    def update_progress(self, total: int, completed: int):
        """更新进度显示"""
        self.total_models = total
        self.completed_models = completed
        
        # 更新进度条
        self.progress_bar.setRange(0, total)
        self.progress_bar.setValue(completed)
        
        # 更新状态标签
        progress_percentage = (completed / total * 100) if total > 0 else 0
        self.status_label.setText(f"测试进度: {completed}/{total} ({progress_percentage:.1f}%)")

    def start_test(self):
        """开始测试"""
        current_config = self.config_combo.currentText()
        if not current_config:
            QMessageBox.warning(self, "错误", "请选择或创建一个配置")
            return
            
        settings = QSettings('AIModelTester', 'Configurations')
        configs = settings.value('configs', {})
        if current_config not in configs:
            QMessageBox.warning(self, "错误", "无法加载选中的配置")
            return
            
        config = configs[current_config]
        api_key = config['api_key']
        base_url = config['base_url']
        
        # 清空结果表格和日志
        self.results_table.setRowCount(0)
        self.log_text.clear()
        self.status_label.setText("准备开始测试...")
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.test_button.setEnabled(False)
        
        # 重置进度计数
        self.total_models = 0
        self.completed_models = 0
        
        # 创建并启动异步工作线程
        self.worker = AsyncModelTestWorker(api_key, base_url)
        self.worker.progress_updated.connect(self.update_log)
        self.worker.test_completed.connect(self.test_finished)
        self.worker.error_occurred.connect(self.handle_error)
        self.worker.result_ready.connect(self.add_test_result)
        self.worker.model_count_updated.connect(self.update_progress)  # 连接新的进度信号
        self.worker.start()
        
        # 保存测试记录
        self.save_test_history(current_config)

    def add_test_result(self, result):
        """添加测试结果到表格和可视化"""
        # 添加到表格
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        # 创建并设置单元格项
        model_item = QTableWidgetItem(result['model_id'])
        status_item = QTableWidgetItem("✓" if result['success'] else "✗")
        latency_item = QTableWidgetItem(f"{result['latency']:.2f}")
        error_item = QTableWidgetItem(result['error'])
        
        # 设置单元格颜色和样式
        if result['success']:
            status_item.setForeground(QBrush(QColor(46, 204, 113)))  # 绿色
            model_item.setBackground(QBrush(QColor(240, 255, 240)))  # 浅绿色背景
        else:
            status_item.setForeground(QBrush(QColor(231, 76, 60)))   # 红色
            model_item.setBackground(QBrush(QColor(255, 240, 240)))  # 浅红色背景
            
        # 设置字体
        font = QFont()
        font.setBold(True)
        status_item.setFont(font)
        
        # 添加到表格
        self.results_table.setItem(row, 0, model_item)
        self.results_table.setItem(row, 1, status_item)
        self.results_table.setItem(row, 2, latency_item)
        self.results_table.setItem(row, 3, error_item)
        
        # 添加到可视化
        self.visualization.add_data_point(result['success'], result['latency'])
        
        # 自动滚动到最新行
        self.results_table.scrollToBottom()

    def update_log(self, text):
        """更新日志"""
        self.log_text.append(f"<span style='color: #2a82da;'>[{datetime.now().strftime('%H:%M:%S')}]</span> {text}")
        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def test_finished(self, working_models):
        """测试完成处理"""
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        self.test_button.setEnabled(True)
        
        # 显示测试总结
        summary = f"\n测试总结:\n"
        summary += f"总共测试了 {len(self.worker.models)} 个模型\n"
        summary += f"其中 {len(working_models)} 个模型可用:\n"
        
        for model_id in working_models:
            summary += f"- {model_id}\n"
            
        recommended_model = ""
        if working_models:
            summary += "\n建议使用的模型:\n"
            if 'claude-3.7-sonnet' in working_models:
                recommended_model = "claude-3.7-sonnet (推荐: 支持中文对话和图片识别)"
                summary += f"- {recommended_model}\n"
            elif any(m.startswith('gpt-4') for m in working_models):
                recommended_model = next(m for m in working_models if m.startswith('gpt-4')) + " (推荐: GPT-4系列)"
                summary += f"- {recommended_model}\n"
            else:
                recommended_model = f"{working_models[0]} (默认推荐)"
                summary += f"- {recommended_model}\n"
                
        self.update_log(summary)

        # 显示完成提示对话框
        msg = QMessageBox(self)
        msg.setWindowTitle("测试完成")
        msg.setIcon(QMessageBox.Icon.Information)
        
        # 设置详细的测试结果信息
        result_text = f"测试已完成！\n\n"
        result_text += f"• 测试模型总数: {len(self.worker.models)} 个\n"
        result_text += f"• 可用模型数量: {len(working_models)} 个\n"
        
        if working_models:
            result_text += f"• 推荐模型: {recommended_model}\n"
            success_rate = (len(working_models) / len(self.worker.models)) * 100
            result_text += f"• 测试成功率: {success_rate:.1f}%\n"
        else:
            result_text += "\n警告：没有发现可用的模型！"
            
        msg.setText(result_text)
        
        # 添加详细按钮，点击可以查看完整日志
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.button(QMessageBox.StandardButton.Ok).setText("确定")
        
        # 显示对话框
        msg.exec()

    def handle_error(self, error_message):
        """错误处理"""
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.test_button.setEnabled(True)
        QMessageBox.critical(self, "错误", error_message)

    def save_test_history(self, config_name):
        """保存测试历史记录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        history_file = self.history_dir / f"test_{timestamp}.json"
        
        settings = QSettings('AIModelTester', 'Configurations')
        configs = settings.value('configs', {})
        config = configs.get(config_name, {})
        
        history = {
            'timestamp': timestamp,
            'config_name': config_name,
            'base_url': config.get('base_url', ''),
            'results': []
        }
        
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()